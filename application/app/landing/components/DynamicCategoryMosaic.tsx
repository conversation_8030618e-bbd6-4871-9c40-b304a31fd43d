/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCamera,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiStar,
	FiSun,
	FiTrendingUp,
	FiUsers,
	FiZap,
} from 'react-icons/fi';

interface CategoryTile {
	id: string;
	name: string;
	subcategories: string[];
	type: 'athletic' | 'non-athletic';
	mood: 'action' | 'chill';
	locationCount: number;
	previewImage?: string;
	gradient: string;
	size: 'small' | 'medium' | 'large';
	icon: React.ReactNode;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CategoryTile) => void;
}

// Import the hook - using dynamic import since auto-formatter removes ES6 imports
import { useCategories } from '../hooks/useCategories';

// Helper function to get icon and type for categories
const getCategoryIcon = (category: string): React.ReactNode => {
	const categoryLower = category.toLowerCase();
	if (
		categoryLower.includes('food') ||
		categoryLower.includes('drink') ||
		categoryLower.includes('restaurant') ||
		categoryLower.includes('cafe')
	) {
		return <FiCoffee className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('sport') ||
		categoryLower.includes('fitness') ||
		categoryLower.includes('gym')
	) {
		return <FiActivity className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('music') ||
		categoryLower.includes('entertainment') ||
		categoryLower.includes('club')
	) {
		return <FiMusic className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('shop') ||
		categoryLower.includes('market') ||
		categoryLower.includes('store')
	) {
		return <FiShoppingBag className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('book') ||
		categoryLower.includes('library') ||
		categoryLower.includes('education')
	) {
		return <FiBook className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('photo') ||
		categoryLower.includes('camera') ||
		categoryLower.includes('art')
	) {
		return <FiCamera className='w-6 h-6' />;
	}
	return <FiMapPin className='w-6 h-6' />; // Default icon
};

const getCategoryType = (category: string): 'athletic' | 'non-athletic' => {
	const categoryLower = category.toLowerCase();
	const athleticKeywords = [
		'sport',
		'fitness',
		'gym',
		'athletic',
		'exercise',
		'training',
		'yoga',
		'swimming',
	];
	return athleticKeywords.some((keyword) => categoryLower.includes(keyword))
		? 'athletic'
		: 'non-athletic';
};

const getCategoryMood = (category: string): 'action' | 'chill' => {
	const categoryLower = category.toLowerCase();
	const actionKeywords = [
		'club',
		'bar',
		'entertainment',
		'sport',
		'fitness',
		'adventure',
		'extreme',
	];
	return actionKeywords.some((keyword) => categoryLower.includes(keyword))
		? 'action'
		: 'chill';
};

const staticCategoryData: CategoryTile[] = [
	// Athletic + Action
	{
		id: 'extreme-sports',
		name: 'Extreme Sports',
		subcategories: [
			'Rock Climbing',
			'Bungee Jumping',
			'Paragliding',
			'Skydiving',
		],
		type: 'athletic',
		mood: 'action',
		locationCount: 23,
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
		size: 'large',
		icon: <FiActivity className='w-6 h-6' />,
	},
	{
		id: 'fitness-centers',
		name: 'Fitness Centers',
		subcategories: ['Gyms', 'CrossFit', 'Boxing', 'Martial Arts'],
		type: 'athletic',
		mood: 'action',
		locationCount: 156,
		gradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
		size: 'medium',
		icon: <FiZap className='w-5 h-5' />,
	},

	// Athletic + Chill
	{
		id: 'yoga-wellness',
		name: 'Yoga & Wellness',
		subcategories: ['Yoga Studios', 'Pilates', 'Meditation Centers', 'Spa'],
		type: 'athletic',
		mood: 'chill',
		locationCount: 89,
		gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		size: 'large',
		icon: <FiHeart className='w-6 h-6' />,
	},
	{
		id: 'walking-trails',
		name: 'Walking Trails',
		subcategories: [
			'Parks',
			'Hiking Trails',
			'Waterfront Walks',
			'Historic Routes',
		],
		type: 'athletic',
		mood: 'chill',
		locationCount: 67,
		gradient: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
		size: 'medium',
		icon: <FiSun className='w-5 h-5' />,
	},

	// Non-Athletic + Action
	{
		id: 'nightlife',
		name: 'Nightlife',
		subcategories: ['Bars', 'Clubs', 'Live Music', 'Rooftop Lounges'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 234,
		gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
		size: 'large',
		icon: <FiMusic className='w-6 h-6' />,
	},
	{
		id: 'entertainment',
		name: 'Entertainment',
		subcategories: ['Theaters', 'Cinemas', 'Comedy Clubs', 'Escape Rooms'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 145,
		gradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.navy} 100%)`,
		size: 'medium',
		icon: <FiStar className='w-5 h-5' />,
	},

	// Non-Athletic + Chill
	{
		id: 'cafes-restaurants',
		name: 'Cafes & Restaurants',
		subcategories: ['Coffee Shops', 'Restaurants', 'Tea Houses', 'Bakeries'],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 1247,
		gradient: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.supporting.lightBlue} 100%)`,
		size: 'large',
		icon: <FiCoffee className='w-6 h-6' />,
	},
	{
		id: 'cultural-spaces',
		name: 'Cultural Spaces',
		subcategories: [
			'Museums',
			'Libraries',
			'Art Galleries',
			'Cultural Centers',
		],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 178,
		gradient: `linear-gradient(135deg, ${colors.ui.gray300} 0%, ${colors.supporting.softNavy} 100%)`,
		size: 'medium',
		icon: <FiBook className='w-5 h-5' />,
	},

	// Additional categories
	{
		id: 'shopping',
		name: 'Shopping',
		subcategories: ['Malls', 'Boutiques', 'Markets', 'Vintage Stores'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 312,
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
		size: 'small',
		icon: <FiShoppingBag className='w-4 h-4' />,
	},
	{
		id: 'photography',
		name: 'Photo Spots',
		subcategories: [
			'Scenic Views',
			'Historic Sites',
			'Street Art',
			'Architecture',
		],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 89,
		gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
		size: 'small',
		icon: <FiCamera className='w-4 h-4' />,
	},
];

const FilterButton: React.FC<{
	active: boolean;
	onClick: () => void;
	children: React.ReactNode;
	icon: React.ReactNode;
}> = ({ active, onClick, children, icon }) => (
	<button
		onClick={onClick}
		className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
			active ? 'scale-105' : 'hover:scale-102'
		}`}
		style={{
			background: active
				? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
				: `linear-gradient(135deg, ${colors.ui.gray100} 0%, ${colors.ui.gray200} 100%)`,
			color: active ? 'white' : colors.neutral.textBlack,
			boxShadow: active ? `0 8px 25px rgba(51, 194, 255, 0.3)` : 'none',
		}}>
		{icon}
		<span className='font-medium'>{children}</span>
	</button>
);

const CategoryCard: React.FC<{
	category: CategoryTile;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ category, isHovered, onHover, onClick }) => {
	const sizeClasses = {
		small: 'col-span-1 row-span-1 h-32',
		medium: 'col-span-2 row-span-1 h-32',
		large: 'col-span-2 row-span-2 h-64',
	};

	return (
		<div
			className={`${
				sizeClasses[category.size]
			} rounded-2xl p-6 cursor-pointer transition-all duration-300 relative overflow-hidden group`}
			style={{
				background: category.gradient,
				transform: isHovered ? 'scale(1.02) translateY(-4px)' : 'scale(1)',
				boxShadow: isHovered
					? '0 20px 40px rgba(0, 0, 0, 0.15)'
					: '0 4px 15px rgba(0, 0, 0, 0.1)',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Background Pattern */}
			<div className='absolute inset-0 opacity-10'>
				<div className='absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]'></div>
			</div>

			{/* Content */}
			<div className='relative h-full flex flex-col justify-between text-white'>
				<div className='flex items-start justify-between'>
					<div className='flex items-center space-x-3'>
						<div className='w-10 h-10 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center'>
							{category.icon}
						</div>
						<div>
							<h3
								className={`font-bold ${
									category.size === 'large' ? 'text-xl' : 'text-lg'
								}`}>
								{category.name}
							</h3>
							<p className='text-white/80 text-sm'>
								{category.locationCount} locations
							</p>
						</div>
					</div>

					<div className='flex items-center space-x-1'>
						{category.type === 'athletic' ? (
							<FiActivity className='w-4 h-4' />
						) : (
							<FiUsers className='w-4 h-4' />
						)}
						{category.mood === 'action' ? (
							<FiZap className='w-4 h-4' />
						) : (
							<FiHeart className='w-4 h-4' />
						)}
					</div>
				</div>

				{/* Subcategories - only show on larger cards or hover */}
				{(category.size === 'large' || isHovered) && (
					<div className='space-y-2'>
						<p className='text-white/60 text-xs uppercase tracking-wide'>
							Subcategories
						</p>
						<div className='flex flex-wrap gap-2'>
							{category.subcategories
								.slice(0, category.size === 'large' ? 4 : 2)
								.map((sub, index) => (
									<span
										key={index}
										className='px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg text-xs'>
										{sub}
									</span>
								))}
							{category.subcategories.length >
								(category.size === 'large' ? 4 : 2) && (
								<span className='px-2 py-1 bg-white/20 backdrop-blur-sm rounded-lg text-xs'>
									+
									{category.subcategories.length -
										(category.size === 'large' ? 4 : 2)}{' '}
									more
								</span>
							)}
						</div>
					</div>
				)}
			</div>

			{/* Hover overlay */}
			<div
				className={`absolute inset-0 bg-white/10 backdrop-blur-sm transition-opacity duration-300 ${
					isHovered ? 'opacity-100' : 'opacity-0'
				}`}></div>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	const [athleticFilter, setAthleticFilter] = useState<
		'all' | 'athletic' | 'non-athletic'
	>('all');
	const [moodFilter, setMoodFilter] = useState<'all' | 'action' | 'chill'>(
		'all'
	);
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

	// Use the real categories hook
	const {
		categories,
		subcategories,
		totalCategories,
		totalSubcategories,
		loading,
		error,
	} = useCategories();

	// Convert real categories to CategoryTile format
	const categoryTiles: CategoryTile[] = categories.map((cat, index) => ({
		id: cat.category.toLowerCase().replace(/\s+/g, '-'),
		name: cat.category,
		subcategories: subcategories
			.filter((sub) =>
				sub.subcategory
					.toLowerCase()
					.includes(cat.category.toLowerCase().split(' ')[0])
			)
			.slice(0, 4)
			.map((sub) => sub.subcategory),
		type: getCategoryType(cat.category),
		mood: getCategoryMood(cat.category),
		locationCount: cat.count,
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
		size: index < 2 ? 'large' : index < 6 ? 'medium' : 'small',
		icon: getCategoryIcon(cat.category),
	}));

	// If we have no real data yet, show static data
	const displayCategories =
		categoryTiles.length > 0 ? categoryTiles : staticCategoryData;

	const filteredCategories = displayCategories.filter((category) => {
		const athleticMatch =
			athleticFilter === 'all' || category.type === athleticFilter;
		const moodMatch = moodFilter === 'all' || category.mood === moodFilter;
		return athleticMatch && moodMatch;
	});

	const totalLocations = filteredCategories.reduce(
		(sum, cat) => sum + cat.locationCount,
		0
	);

	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{totalSubcategories}+ Subcategories • {totalCategories}+ Main
							Categories
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Explore</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Every Category
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						From adrenaline-pumping adventures to peaceful retreats. Discover{' '}
						{totalLocations.toLocaleString()} locations across Istanbul.
					</p>
				</div>

				{/* Filter Controls */}
				<div className='flex flex-wrap justify-center gap-4 mb-12'>
					<div className='flex items-center space-x-3'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Category:
						</span>
						<FilterButton
							active={athleticFilter === 'all'}
							onClick={() => setAthleticFilter('all')}
							icon={<FiTrendingUp className='w-4 h-4' />}>
							All
						</FilterButton>
						<FilterButton
							active={athleticFilter === 'athletic'}
							onClick={() => setAthleticFilter('athletic')}
							icon={<FiActivity className='w-4 h-4' />}>
							Athletic
						</FilterButton>
						<FilterButton
							active={athleticFilter === 'non-athletic'}
							onClick={() => setAthleticFilter('non-athletic')}
							icon={<FiUsers className='w-4 h-4' />}>
							Non-Athletic
						</FilterButton>
					</div>

					<div className='flex items-center space-x-3'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Subcategory:
						</span>
						<FilterButton
							active={moodFilter === 'all'}
							onClick={() => setMoodFilter('all')}
							icon={<FiStar className='w-4 h-4' />}>
							All
						</FilterButton>
						<FilterButton
							active={moodFilter === 'action'}
							onClick={() => setMoodFilter('action')}
							icon={<FiZap className='w-4 h-4' />}>
							Restaurant
						</FilterButton>
						<FilterButton
							active={moodFilter === 'chill'}
							onClick={() => setMoodFilter('chill')}
							icon={<FiHeart className='w-4 h-4' />}>
							Cafe
						</FilterButton>
					</div>

					<div className='flex items-center space-x-3'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Tags:
						</span>
						<FilterButton
							active={true}
							onClick={() => {}}
							icon={<FiMapPin className='w-4 h-4' />}>
							Outdoor
						</FilterButton>
						<FilterButton
							active={false}
							onClick={() => {}}
							icon={<FiCoffee className='w-4 h-4' />}>
							WiFi
						</FilterButton>
						<FilterButton
							active={false}
							onClick={() => {}}
							icon={<FiHeart className='w-4 h-4' />}>
							Romantic
						</FilterButton>
					</div>
				</div>

				{/* Category Grid */}
				<div className='grid grid-cols-4 gap-6 auto-rows-fr'>
					{filteredCategories.map((category) => (
						<CategoryCard
							key={category.id}
							category={category}
							isHovered={hoveredCategory === category.id}
							onHover={(hovered) =>
								setHoveredCategory(hovered ? category.id : null)
							}
							onClick={() => onCategorySelect?.(category)}
						/>
					))}
				</div>

				{/* Stats Footer */}
				<div className='mt-12 text-center'>
					<p
						className='text-lg'
						style={{ color: colors.neutral.slateGray }}>
						Showing{' '}
						<span
							className='font-bold'
							style={{ color: colors.brand.blue }}>
							{filteredCategories.length}
						</span>{' '}
						categories with{' '}
						<span
							className='font-bold'
							style={{ color: colors.brand.green }}>
							{totalLocations.toLocaleString()}
						</span>{' '}
						total locations
					</p>
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
