/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCamera,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiStar,
	FiSun,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

interface CategoryTile {
	id: string;
	name: string;
	subcategories: string[];
	type: 'athletic' | 'non-athletic';
	mood: 'action' | 'chill';
	locationCount: number;
	previewImage?: string;
	gradient: string;
	size: 'small' | 'medium' | 'large';
	icon: React.ReactNode;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CategoryTile) => void;
}

// Import the hook - using dynamic import since auto-formatter removes ES6 imports

// Helper function to get icon and type for categories
const getCategoryIcon = (category: string): React.ReactNode => {
	const categoryLower = category.toLowerCase();
	if (
		categoryLower.includes('food') ||
		categoryLower.includes('drink') ||
		categoryLower.includes('restaurant') ||
		categoryLower.includes('cafe')
	) {
		return <FiCoffee className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('sport') ||
		categoryLower.includes('fitness') ||
		categoryLower.includes('gym')
	) {
		return <FiActivity className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('music') ||
		categoryLower.includes('entertainment') ||
		categoryLower.includes('club')
	) {
		return <FiMusic className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('shop') ||
		categoryLower.includes('market') ||
		categoryLower.includes('store')
	) {
		return <FiShoppingBag className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('book') ||
		categoryLower.includes('library') ||
		categoryLower.includes('education')
	) {
		return <FiBook className='w-6 h-6' />;
	}
	if (
		categoryLower.includes('photo') ||
		categoryLower.includes('camera') ||
		categoryLower.includes('art')
	) {
		return <FiCamera className='w-6 h-6' />;
	}
	return <FiMapPin className='w-6 h-6' />; // Default icon
};

const getCategoryType = (category: string): 'athletic' | 'non-athletic' => {
	const categoryLower = category.toLowerCase();
	const athleticKeywords = [
		'sport',
		'fitness',
		'gym',
		'athletic',
		'exercise',
		'training',
		'yoga',
		'swimming',
	];
	return athleticKeywords.some((keyword) => categoryLower.includes(keyword))
		? 'athletic'
		: 'non-athletic';
};

const getCategoryMood = (category: string): 'action' | 'chill' => {
	const categoryLower = category.toLowerCase();
	const actionKeywords = [
		'club',
		'bar',
		'entertainment',
		'sport',
		'fitness',
		'adventure',
		'extreme',
	];
	return actionKeywords.some((keyword) => categoryLower.includes(keyword))
		? 'action'
		: 'chill';
};

const staticCategoryData: CategoryTile[] = [
	// Athletic + Action
	{
		id: 'extreme-sports',
		name: 'Extreme Sports',
		subcategories: [
			'Rock Climbing',
			'Bungee Jumping',
			'Paragliding',
			'Skydiving',
		],
		type: 'athletic',
		mood: 'action',
		locationCount: 23,
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
		size: 'large',
		icon: <FiActivity className='w-6 h-6' />,
	},
	{
		id: 'fitness-centers',
		name: 'Fitness Centers',
		subcategories: ['Gyms', 'CrossFit', 'Boxing', 'Martial Arts'],
		type: 'athletic',
		mood: 'action',
		locationCount: 156,
		gradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
		size: 'medium',
		icon: <FiZap className='w-5 h-5' />,
	},

	// Athletic + Chill
	{
		id: 'yoga-wellness',
		name: 'Yoga & Wellness',
		subcategories: ['Yoga Studios', 'Pilates', 'Meditation Centers', 'Spa'],
		type: 'athletic',
		mood: 'chill',
		locationCount: 89,
		gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		size: 'large',
		icon: <FiHeart className='w-6 h-6' />,
	},
	{
		id: 'walking-trails',
		name: 'Walking Trails',
		subcategories: [
			'Parks',
			'Hiking Trails',
			'Waterfront Walks',
			'Historic Routes',
		],
		type: 'athletic',
		mood: 'chill',
		locationCount: 67,
		gradient: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.brand.green} 100%)`,
		size: 'medium',
		icon: <FiSun className='w-5 h-5' />,
	},

	// Non-Athletic + Action
	{
		id: 'nightlife',
		name: 'Nightlife',
		subcategories: ['Bars', 'Clubs', 'Live Music', 'Rooftop Lounges'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 234,
		gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
		size: 'large',
		icon: <FiMusic className='w-6 h-6' />,
	},
	{
		id: 'entertainment',
		name: 'Entertainment',
		subcategories: ['Theaters', 'Cinemas', 'Comedy Clubs', 'Escape Rooms'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 145,
		gradient: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.navy} 100%)`,
		size: 'medium',
		icon: <FiStar className='w-5 h-5' />,
	},

	// Non-Athletic + Chill
	{
		id: 'cafes-restaurants',
		name: 'Cafes & Restaurants',
		subcategories: ['Coffee Shops', 'Restaurants', 'Tea Houses', 'Bakeries'],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 1247,
		gradient: `linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.supporting.lightBlue} 100%)`,
		size: 'large',
		icon: <FiCoffee className='w-6 h-6' />,
	},
	{
		id: 'cultural-spaces',
		name: 'Cultural Spaces',
		subcategories: [
			'Museums',
			'Libraries',
			'Art Galleries',
			'Cultural Centers',
		],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 178,
		gradient: `linear-gradient(135deg, ${colors.ui.gray300} 0%, ${colors.supporting.softNavy} 100%)`,
		size: 'medium',
		icon: <FiBook className='w-5 h-5' />,
	},

	// Additional categories
	{
		id: 'shopping',
		name: 'Shopping',
		subcategories: ['Malls', 'Boutiques', 'Markets', 'Vintage Stores'],
		type: 'non-athletic',
		mood: 'action',
		locationCount: 312,
		gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
		size: 'small',
		icon: <FiShoppingBag className='w-4 h-4' />,
	},
	{
		id: 'photography',
		name: 'Photo Spots',
		subcategories: [
			'Scenic Views',
			'Historic Sites',
			'Street Art',
			'Architecture',
		],
		type: 'non-athletic',
		mood: 'chill',
		locationCount: 89,
		gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
		size: 'small',
		icon: <FiCamera className='w-4 h-4' />,
	},
];

const FilterButton: React.FC<{
	active: boolean;
	onClick: () => void;
	children: React.ReactNode;
	icon: React.ReactNode;
}> = ({ active, onClick, children, icon }) => (
	<button
		onClick={onClick}
		className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
			active ? 'scale-105' : 'hover:scale-102'
		}`}
		style={{
			background: active
				? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
				: `linear-gradient(135deg, ${colors.ui.gray100} 0%, ${colors.ui.gray200} 100%)`,
			color: active ? 'white' : colors.neutral.textBlack,
			boxShadow: active ? `0 8px 25px rgba(51, 194, 255, 0.3)` : 'none',
		}}>
		{icon}
		<span className='font-medium'>{children}</span>
	</button>
);

const CategoryCard: React.FC<{
	category: any;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ category, isHovered, onHover, onClick }) => {
	// Dynamic design elements based on card size/priority
	const getDesignElements = () => {
		const size = category.size;
		const priority = category.priority || 50;

		// Larger cards get more elaborate designs
		if (priority > 80) {
			return {
				pattern:
					'bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.2)_2px,transparent_2px)] bg-[length:30px_30px]',
				iconSize: 'w-16 h-16',
				iconBg: 'bg-white/60',
				textSize: 'text-xl',
				decoration: (
					<div className='absolute top-4 right-4 w-20 h-20 rounded-full bg-white/10 blur-xl'></div>
				),
			};
		} else if (priority > 60) {
			return {
				pattern:
					'bg-[linear-gradient(45deg,rgba(255,255,255,0.1)_25%,transparent_25%,transparent_75%,rgba(255,255,255,0.1)_75%)] bg-[length:20px_20px]',
				iconSize: 'w-12 h-12',
				iconBg: 'bg-white/55',
				textSize: 'text-lg',
				decoration: (
					<div className='absolute -right-4 -top-4 w-16 h-16 rounded-full bg-white/5'></div>
				),
			};
		} else if (priority > 40) {
			return {
				pattern:
					'bg-[conic-gradient(from_0deg_at_50%_50%,rgba(255,255,255,0.1)_0deg,transparent_60deg,rgba(255,255,255,0.1)_120deg,transparent_180deg)]',
				iconSize: 'w-10 h-10',
				iconBg: 'bg-white/50',
				textSize: 'text-base',
				decoration: (
					<div className='absolute bottom-2 left-2 w-12 h-12 rounded-lg bg-white/8 rotate-12'></div>
				),
			};
		} else {
			return {
				pattern:
					'bg-[radial-gradient(circle_at_70%_20%,rgba(255,255,255,0.15)_1px,transparent_1px)] bg-[length:15px_15px]',
				iconSize: 'w-8 h-8',
				iconBg: 'bg-white/45',
				textSize: 'text-sm',
				decoration: (
					<div className='absolute top-2 right-2 w-6 h-6 rounded-full bg-white/10'></div>
				),
			};
		}
	};

	const designElements = getDesignElements();

	// Stock market performance indicator
	const getPerformanceColor = () => {
		const performance = Math.random() > 0.5 ? 'up' : 'down';
		return performance === 'up'
			? 'border-green-400/50 bg-gradient-to-br from-green-900/80 to-green-800/60'
			: 'border-red-400/50 bg-gradient-to-br from-red-900/80 to-red-800/60';
	};

	const performanceChange = ((Math.random() - 0.5) * 20).toFixed(2);
	const isPositive = parseFloat(performanceChange) > 0;

	return (
		<div
			className={`rounded-lg border-2 cursor-pointer transition-all duration-300 relative overflow-hidden group ${getPerformanceColor()}`}
			style={{
				width: `${category.size?.width || 200}px`,
				height: `${category.size?.height || 150}px`,
				transform: isHovered ? 'scale(1.05) translateY(-2px)' : 'scale(1)',
				boxShadow: isHovered
					? '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2)'
					: '0 4px 15px rgba(0, 0, 0, 0.2)',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Stock market background pattern */}
			<div className='absolute inset-0 opacity-30'>
				<div className={`absolute inset-0 ${designElements.pattern}`}></div>
			</div>

			{/* Performance indicator */}
			<div className='absolute top-2 right-2'>
				<div
					className={`px-2 py-1 rounded text-xs font-bold ${
						isPositive
							? 'bg-green-500/80 text-green-100'
							: 'bg-red-500/80 text-red-100'
					}`}>
					{isPositive ? '+' : ''}
					{performanceChange}%
				</div>
			</div>

			{/* Content */}
			<div className='relative h-full flex flex-col justify-between text-white p-3'>
				{/* Header */}
				<div className='flex items-start justify-between'>
					<div
						className={`${designElements.iconSize} rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center`}>
						{category.icon}
					</div>
					<div className='text-right'>
						<div className='text-xs text-white/70'>Market Cap</div>
						<div className='text-sm font-bold'>
							{category.priority?.toFixed(1)}%
						</div>
					</div>
				</div>

				{/* Title */}
				<div className='space-y-1'>
					<h3 className={`font-bold ${designElements.textSize} leading-tight`}>
						{category.subcategory}
					</h3>
					<p className='text-white/80 text-xs uppercase tracking-wide'>
						{category.category}
					</p>
				</div>

				{/* Stock chart simulation */}
				<div className='flex items-end space-x-1 h-6'>
					{Array.from({ length: 8 }).map((_, i) => (
						<div
							key={i}
							className={`w-1 rounded-sm ${
								isPositive ? 'bg-green-400' : 'bg-red-400'
							}`}
							style={{ height: `${Math.random() * 100}%` }}
						/>
					))}
				</div>
			</div>

			{/* Hover glow effect */}
			<div
				className={`absolute inset-0 bg-blue-400/10 backdrop-blur-sm transition-opacity duration-300 ${
					isHovered ? 'opacity-100' : 'opacity-0'
				}`}></div>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	// Use the actual categories from constants (hardcoded for now since import isn't working)
	const ACTUAL_CATEGORIES = {
		'Food & Drink': {
			subcategories: ['Cafe', 'Restaurant'],
		},
		'Cultural & Creative Experiences': {
			subcategories: ['Museum Visit', 'Art Gallery Walk'],
		},
		'Cultural & Creative Experiences2': {
			subcategories: ['Museum Visit2', 'Art Gallery Walk2'],
		},
	};

	// Randomization utilities
	const shuffleArray = <T,>(array: T[]): T[] => {
		const shuffled = [...array];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	};

	// Get categories for display (max 3, rest in dropdown)
	const getDisplayCategories = () => {
		const allCategories = Object.keys(ACTUAL_CATEGORIES);
		if (allCategories.length <= 3) {
			return { visible: allCategories, dropdown: [] };
		}
		const shuffled = shuffleArray(allCategories);
		return {
			visible: shuffled.slice(0, 3),
			dropdown: shuffled.slice(3),
		};
	};

	// Get subcategories for display based on selected category
	const getDisplaySubcategories = (selectedCategory: string) => {
		if (selectedCategory === 'all') {
			// Get all subcategories from all categories, randomized, max 10
			const allSubcategories: string[] = [];
			Object.values(ACTUAL_CATEGORIES).forEach((cat) => {
				allSubcategories.push(...cat.subcategories);
			});
			const shuffled = shuffleArray(allSubcategories);
			return shuffled.slice(0, 10);
		} else {
			// Get subcategories for specific category
			const categoryData =
				ACTUAL_CATEGORIES[selectedCategory as keyof typeof ACTUAL_CATEGORIES];
			if (categoryData) {
				return shuffleArray(categoryData.subcategories);
			}
		}
		return [];
	};

	// Create different gradients for variety
	const getCardGradient = (index: number) => {
		const gradients = [
			`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
			`linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
			`linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.softNavy} 100%)`,
			`linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
			`linear-gradient(135deg, ${colors.supporting.mintGreen} 0%, ${colors.supporting.lightBlue} 100%)`,
		];
		return gradients[index % gradients.length];
	};

	// Get different icons for variety
	const getCardIcon = (subcategory: string) => {
		const iconMap: { [key: string]: JSX.Element } = {
			Cafe: <FiCoffee className='w-6 h-6' />,
			Restaurant: <FiSun className='w-6 h-6' />,
			'Museum Visit': <FiBook className='w-6 h-6' />,
			'Art Gallery Walk': <FiCamera className='w-6 h-6' />,
			'Museum Visit2': <FiMusic className='w-6 h-6' />,
			'Art Gallery Walk2': <FiShoppingBag className='w-6 h-6' />,
		};
		return iconMap[subcategory] || <FiMapPin className='w-6 h-6' />;
	};

	// True stock market style sizing algorithm with boundary fitting
	const calculateCardSizes = (cardCount: number) => {
		// Container dimensions with padding
		const containerWidth = 1168; // 1200 - 32px padding
		const containerHeight = 768; // 800 - 32px padding
		const gap = 16;

		// Generate realistic stock market weights (some very large, some small)
		const weights = [];
		for (let i = 0; i < cardCount; i++) {
			if (i === 0) weights.push(100); // Largest stock
			else if (i === 1) weights.push(60); // Second largest
			else if (i === 2) weights.push(40); // Third largest
			else weights.push(Math.random() * 25 + 10); // Smaller stocks
		}

		const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

		// Calculate sizes that fit within boundaries
		const cardSizes = weights.map((weight, index) => {
			const percentage = weight / totalWeight;

			// Base sizes that ensure fitting
			let width, height;

			if (percentage > 0.3) {
				// Largest card
				width = Math.min(400, containerWidth * 0.4);
				height = Math.min(300, containerHeight * 0.4);
			} else if (percentage > 0.2) {
				// Second largest
				width = Math.min(300, containerWidth * 0.3);
				height = Math.min(250, containerHeight * 0.3);
			} else if (percentage > 0.1) {
				// Medium cards
				width = Math.min(250, containerWidth * 0.25);
				height = Math.min(200, containerHeight * 0.25);
			} else if (percentage > 0.05) {
				// Small cards
				width = Math.min(200, containerWidth * 0.2);
				height = Math.min(150, containerHeight * 0.2);
			} else {
				// Tiny cards
				width = Math.min(150, containerWidth * 0.15);
				height = Math.min(120, containerHeight * 0.15);
			}

			return {
				width: Math.floor(width),
				height: Math.floor(height),
				weight: percentage * 100,
				priority: percentage * 100,
			};
		});

		return cardSizes;
	};

	// Create randomized subcategory cards with stock market sizing
	const createSubcategoryCards = () => {
		const allCards: any[] = [];
		let cardIndex = 0;

		// Create all possible cards
		Object.entries(ACTUAL_CATEGORIES).forEach(
			([categoryName, categoryData]) => {
				categoryData.subcategories.forEach((subcategory) => {
					allCards.push({
						id: `${categoryName}-${subcategory}`,
						category: categoryName,
						subcategory: subcategory,
						gradient: getCardGradient(cardIndex),
						icon: getCardIcon(subcategory),
					});
					cardIndex++;
				});
			}
		);

		// Randomize and limit to 10 cards
		const shuffledCards = shuffleArray(allCards);
		const selectedCards = shuffledCards.slice(0, 10);

		// Calculate sizes for the selected cards
		const cardSizes = calculateCardSizes(selectedCards.length);

		// Assign sizes to cards
		return selectedCards.map((card, index) => ({
			...card,
			size: cardSizes[index],
			priority: cardSizes[index].weight, // Higher weight = higher priority
		}));
	};

	const [displayCategories] = useState(getDisplayCategories());
	const [displayCards, setDisplayCards] = useState(() =>
		createSubcategoryCards()
	);

	const [categoryFilter, setCategoryFilter] = useState<string>('all');
	const [subcategoryFilter, setSubcategoryFilter] = useState<string>('all');
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

	// Function to randomize cards
	const randomizeCards = () => {
		setDisplayCards(createSubcategoryCards());
	};

	// Reset subcategory filter when category changes
	const handleCategoryChange = (newCategory: string) => {
		setCategoryFilter(newCategory);
		setSubcategoryFilter('all'); // Reset subcategory when category changes
	};

	const filteredCards = displayCards.filter((card) => {
		const categoryMatch =
			categoryFilter === 'all' || card.category === categoryFilter;
		const subcategoryMatch =
			subcategoryFilter === 'all' || card.subcategory === subcategoryFilter;
		return categoryMatch && subcategoryMatch;
	});

	// Stock market style container - fixed boundary with proper packing
	const getContainerStyle = () => {
		return {
			width: '1200px',
			height: '800px',
			maxWidth: '100%',
			margin: '0 auto',
			padding: '16px',
			border: '3px solid rgba(59, 130, 246, 0.3)', // Blue border like trading platforms
			borderRadius: '16px',
			background:
				'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%)',
			boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
		};
	};

	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{displayCards.length} Subcategories •{' '}
							{Object.keys(ACTUAL_CATEGORIES).length} Main Categories
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>Explore</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							Every Category
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						From adrenaline-pumping adventures to peaceful retreats. Discover
						amazing locations across Istanbul.
					</p>
				</div>

				{/* Randomize Button */}
				<div className='flex justify-center mb-6'>
					<button
						onClick={randomizeCards}
						className='px-6 py-2 bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-lg hover:from-blue-600 hover:to-green-600 transition-all duration-300 flex items-center space-x-2'>
						<FiStar className='w-4 h-4' />
						<span>Randomize Cards</span>
					</button>
				</div>

				{/* Filter Controls */}
				<div className='flex flex-wrap justify-center gap-4 mb-12'>
					<div className='flex items-center space-x-3 flex-wrap'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Category:
						</span>
						<FilterButton
							active={categoryFilter === 'all'}
							onClick={() => handleCategoryChange('all')}
							icon={<FiTrendingUp className='w-4 h-4' />}>
							All
						</FilterButton>
						{/* Show first 3 categories as buttons */}
						{displayCategories.visible.map((category) => (
							<FilterButton
								key={category}
								active={categoryFilter === category}
								onClick={() => handleCategoryChange(category)}
								icon={<FiActivity className='w-4 h-4' />}>
								{category}
							</FilterButton>
						))}
						{/* Show dropdown for additional categories */}
						{displayCategories.dropdown.length > 0 && (
							<div className='relative'>
								<select
									value={
										displayCategories.dropdown.includes(categoryFilter)
											? categoryFilter
											: ''
									}
									onChange={(e) =>
										e.target.value && handleCategoryChange(e.target.value)
									}
									className='px-3 py-1 rounded-lg border border-gray-300 text-sm bg-white'>
									<option value=''>More Categories...</option>
									{displayCategories.dropdown.map((category) => (
										<option
											key={category}
											value={category}>
											{category}
										</option>
									))}
								</select>
							</div>
						)}
					</div>

					<div className='flex items-center space-x-3 flex-wrap'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Subcategory:
						</span>
						<FilterButton
							active={subcategoryFilter === 'all'}
							onClick={() => setSubcategoryFilter('all')}
							icon={<FiStar className='w-4 h-4' />}>
							All
						</FilterButton>
						{(() => {
							const displaySubcategories =
								getDisplaySubcategories(categoryFilter);
							const visibleSubcategories = displaySubcategories.slice(0, 3);
							const dropdownSubcategories = displaySubcategories.slice(3);

							return (
								<>
									{/* Show first 3 subcategories as buttons */}
									{visibleSubcategories.map((subcategory) => (
										<FilterButton
											key={subcategory}
											active={subcategoryFilter === subcategory}
											onClick={() => setSubcategoryFilter(subcategory)}
											icon={<FiZap className='w-4 h-4' />}>
											{subcategory}
										</FilterButton>
									))}
									{/* Show dropdown for additional subcategories */}
									{dropdownSubcategories.length > 0 && (
										<div className='relative'>
											<select
												value={
													dropdownSubcategories.includes(subcategoryFilter)
														? subcategoryFilter
														: ''
												}
												onChange={(e) =>
													e.target.value && setSubcategoryFilter(e.target.value)
												}
												className='px-3 py-1 rounded-lg border border-gray-300 text-sm bg-white'>
												<option value=''>More Subcategories...</option>
												{dropdownSubcategories.map((subcategory) => (
													<option
														key={subcategory}
														value={subcategory}>
														{subcategory}
													</option>
												))}
											</select>
										</div>
									)}
								</>
							);
						})()}
					</div>
				</div>

				{/* Stock Market Style Card Container */}
				<div
					style={getContainerStyle()}
					className='relative overflow-hidden'>
					{/* Trading platform background */}
					<div className='absolute inset-0 opacity-20'>
						<div className='absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:40px_40px]'></div>
					</div>

					{/* Market header */}
					<div className='relative mb-4 pb-2 border-b border-blue-400/30'>
						<h3 className='text-blue-300 text-sm font-medium'>
							Market Overview
						</h3>
						<p className='text-blue-200/70 text-xs'>
							Live subcategory performance
						</p>
					</div>

					{/* Stock market card layout - proper packing */}
					<div
						className='relative'
						style={{ height: 'calc(100% - 60px)' }}>
						<div
							className='absolute inset-0 flex flex-wrap content-start gap-3'
							style={{
								alignItems: 'flex-start',
								justifyContent: 'flex-start',
								overflow: 'hidden',
							}}>
							{filteredCards
								.sort((a, b) => (b.priority || 0) - (a.priority || 0)) // Sort by priority (largest first)
								.map((card) => (
									<CategoryCard
										key={card.id}
										category={card}
										isHovered={hoveredCategory === card.id}
										onHover={(hovered) =>
											setHoveredCategory(hovered ? card.id : null)
										}
										onClick={() => onCategorySelect?.(card)}
									/>
								))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
